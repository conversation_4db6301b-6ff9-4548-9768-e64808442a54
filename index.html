<html>
  <body>
    <div id="app"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/babel">
        // const app = document.getElementById('app');

        // const header = document.createElement('h1');

        // const text = 'Develop. Preview. Ship.';
        // const headerContent = document.createTextNode(text);
        // header.appendChild(headerContent);

        // app.appendChild(header);

        const app = document.getElementById('app');
        const root = ReactDOM.createRoot(app);
        // root.render(React.createElement('h1', null, 'Develop. Preview. Ship.'));
        
        function Header(props) {
            console.log(props.title)
            return <h1>{`cool ${props.title}`}</h1>;
        }

        function HomePage() {

            function handleClick() {
                console.log('Clicked!');
            }

            const names = ['Ada Lovelace', 'Grace Hopper', 'Margaret Hamilton'];
            return (
                <div>
                    <Header title="Develp Preview ship" />
                    <p>This is the home page.</p>
                    <ul>
                        {names.map((name) => (
                            <li key={name}>{name}</li>
                        ))}
                    </ul>
                    <button onClick={handleClick}>Click me</button>
                </div>
            )
        }
        
        // root.render(<h1>Develop. Preview. Ship-second.</h1>);
        root.render(<HomePage />);
    </script>

  </body>
</html>