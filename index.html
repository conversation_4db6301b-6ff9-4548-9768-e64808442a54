<html>
  <body>
    <div id="app"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/jsx">
        // const app = document.getElementById('app');

        // const header = document.createElement('h1');

        // const text = 'Develop. Preview. Ship.';
        // const headerContent = document.createTextNode(text);
        // header.appendChild(headerContent);

        // app.appendChild(header);

        const app = document.getElementById('app');
        const root = ReactDOM.createRoot(app);
        // root.render(React.createElement('h1', null, 'Develop. Preview. Ship.'));
        
        function Header() {
            return <h1>Develop. Preview. Ship.</h1>;
        }
        
        <!-- root.render(<h1>Develop. Preview. Ship-new.</h1>); -->
        root
    </script>

  </body>
</html>