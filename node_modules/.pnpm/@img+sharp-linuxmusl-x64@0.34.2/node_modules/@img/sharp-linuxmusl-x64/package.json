{"name": "@img/sharp-linuxmusl-x64", "version": "0.34.2", "description": "Prebuilt sharp for use with Linux (musl) x64", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://sharp.pixelplumbing.com", "repository": {"type": "git", "url": "git+https://github.com/lovell/sharp.git", "directory": "npm/linuxmusl-x64"}, "license": "Apache-2.0", "funding": {"url": "https://opencollective.com/libvips"}, "preferUnplugged": true, "optionalDependencies": {"@img/sharp-libvips-linuxmusl-x64": "1.1.0"}, "files": ["lib"], "publishConfig": {"access": "public"}, "type": "commonjs", "exports": {"./sharp.node": "./lib/sharp-linuxmusl-x64.node", "./package": "./package.json"}, "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "config": {"musl": ">=1.2.2"}, "os": ["linux"], "libc": ["musl"], "cpu": ["x64"]}