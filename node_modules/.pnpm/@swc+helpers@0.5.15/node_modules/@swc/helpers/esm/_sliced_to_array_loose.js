import { _ as _array_with_holes } from "./_array_with_holes.js";
import { _ as _iterable_to_array_limit_loose } from "./_iterable_to_array_limit_loose.js";
import { _ as _non_iterable_rest } from "./_non_iterable_rest.js";
import { _ as _unsupported_iterable_to_array } from "./_unsupported_iterable_to_array.js";

function _sliced_to_array_loose(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit_loose(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
export { _sliced_to_array_loose as _ };
